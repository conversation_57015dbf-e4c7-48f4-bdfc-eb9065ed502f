const { compareProductsAsync } = require('../../services/product/productCompareAsyncService');
const { success, error } = require('../../utils/response');
const { compareProductsV4ParallelSchema } = require('../../validators/productvalidator');

/**
 * 异步产品对比控制器
 * 处理异步产品对比相关的API请求
 */

/**
 * @desc    异步产品对比 - 检查缓存并返回结果或启动后台任务
 * @route   POST /api/v1/products/compare-async
 * @access  Private (需要用户认证)
 */
const compareProductsAsyncController = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsV4ParallelSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames } = value;
    const userId = req.user?.id; // 从认证中间件获取用户ID

    if (!userId) {
      return error(res, 401, '用户未认证，请先登录');
    }

    console.log('🚀 开始异步产品对比，产品列表:', productNames);
    console.log('🔍 用户ID:', userId);

    // 调用异步产品对比服务
    const result = await compareProductsAsync(productNames, userId);

    if (!result.success) {
      console.error('❌ 异步产品对比失败:', result.error);
      return error(res, 400, result.error, result.data);
    }

    console.log('✅ 异步产品对比请求处理完成');

    // 返回成功响应
    const responseData = {
      ...result.data,
      meta: {
        requestedProducts: productNames,
        actualProductCount: result.data.products.length,
        userId: userId,
        requestTime: new Date().toISOString(),
        processingMode: result.data.isAsync ? 'async' : 'cached',
        aiModel: result.data.aiAnalysis?.aiModel || 'deepseek-chat',
        description: result.data.isAsync
          ? '异步产品对比任务已启动，完成后将通过通知告知'
          : '从缓存返回产品对比结果'
      }
    };

    const message = result.data.isAsync 
      ? '产品对比任务已启动，完成后将通过通知告知您'
      : '产品对比结果获取成功';

    return success(res, 200, message, responseData);

  } catch (err) {
    console.error('异步产品对比控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('至少需要提供')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('最多支持')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('用户ID不能为空')) {
      return error(res, 401, '用户认证失败，请重新登录');
    }

    return error(res, 500, '异步产品对比服务暂时不可用，请稍后重试');
  }
};

module.exports = {
  compareProductsAsyncController
};
